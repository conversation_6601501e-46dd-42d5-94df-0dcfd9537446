# Git
.git
.gitignore

# IDE
.idea
.vscode
*.iml

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Build outputs
target/

# Temporary folders
tmp/
temp/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.gitlab-ci.yml
.github/

# Documentation
README.md
QUICK_START.md
*.md

# Test files
src/test/
test/
