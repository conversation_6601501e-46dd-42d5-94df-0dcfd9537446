package com.yunzheng.capability.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

@TableName("script_execution")
public class ScriptExecution {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("server_id")
    private Long serverId;
    
    @TableField("script_content")
    private String scriptContent;
    
    @TableField("execution_result")
    private String executionResult;
    
    @TableField("error_message")
    private String errorMessage;
    
    @TableField(value = "status", typeHandler = org.apache.ibatis.type.EnumTypeHandler.class)
    private ExecutionStatus status;
    
    @TableField("execution_time")
    private Integer executionTime; // 执行时间，单位：毫秒
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    public enum ExecutionStatus {
        RUNNING, SUCCESS, FAILED
    }
    
    // 无参构造函数
    public ScriptExecution() {}
    
    // 有参构造函数
    public ScriptExecution(Long serverId, String scriptContent) {
        this.serverId = serverId;
        this.scriptContent = scriptContent;
        this.status = ExecutionStatus.RUNNING;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getServerId() {
        return serverId;
    }
    
    public void setServerId(Long serverId) {
        this.serverId = serverId;
    }
    
    public String getScriptContent() {
        return scriptContent;
    }
    
    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }
    
    public String getExecutionResult() {
        return executionResult;
    }
    
    public void setExecutionResult(String executionResult) {
        this.executionResult = executionResult;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public ExecutionStatus getStatus() {
        return status;
    }
    
    public void setStatus(ExecutionStatus status) {
        this.status = status;
    }
    
    public Integer getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(Integer executionTime) {
        this.executionTime = executionTime;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "ScriptExecution{" +
                "id=" + id +
                ", serverId=" + serverId +
                ", scriptContent='" + scriptContent + '\'' +
                ", executionResult='" + executionResult + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", status=" + status +
                ", executionTime=" + executionTime +
                ", createdAt=" + createdAt +
                '}';
    }
}
