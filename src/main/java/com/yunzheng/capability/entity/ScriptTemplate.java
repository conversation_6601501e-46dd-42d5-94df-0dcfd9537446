package com.yunzheng.capability.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

@TableName("script_template")
public class ScriptTemplate {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String name;
    
    @TableField("script_content")
    private String scriptContent;
    
    private String description;
    
    private String category;
    
    @TableField("is_public")
    private Boolean isPublic;
    
    @TableField("created_by")
    private String createdBy;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    // 构造函数
    public ScriptTemplate() {}
    
    public ScriptTemplate(String name, String scriptContent, String description, String category) {
        this.name = name;
        this.scriptContent = scriptContent;
        this.description = description;
        this.category = category;
        this.isPublic = false;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getScriptContent() {
        return scriptContent;
    }
    
    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Boolean getIsPublic() {
        return isPublic;
    }
    
    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
