package com.yunzheng.capability.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * Webhook日志记录实体
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@TableName("webhook_log")
public class WebhookLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 请求来源标识（如：gitlab、github等）
     */
    private String source;
    
    /**
     * 接收到的JSON数据
     */
    private String jsonData;
    
    /**
     * 推送到飞书的状态（SUCCESS、FAILED、PENDING）
     */
    private String pushStatus;
    
    /**
     * 飞书推送结果
     */
    private String pushResult;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // 构造函数
    public WebhookLog() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getJsonData() {
        return jsonData;
    }
    
    public void setJsonData(String jsonData) {
        this.jsonData = jsonData;
    }
    
    public String getPushStatus() {
        return pushStatus;
    }
    
    public void setPushStatus(String pushStatus) {
        this.pushStatus = pushStatus;
    }
    
    public String getPushResult() {
        return pushResult;
    }
    
    public void setPushResult(String pushResult) {
        this.pushResult = pushResult;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
