package com.yunzheng.capability.service;

import com.yunzheng.capability.entity.ScriptExecution;
import com.yunzheng.capability.entity.ServerConfig;
import com.yunzheng.capability.mapper.ScriptExecutionMapper;
import com.yunzheng.capability.mapper.ServerConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
public class ScriptExecutionService {
    
    private static final Logger logger = LoggerFactory.getLogger(ScriptExecutionService.class);
    
    @Autowired
    private ScriptExecutionMapper scriptExecutionMapper;
    
    @Autowired
    private ServerConfigMapper serverConfigMapper;
    
    @Autowired
    private SSHService sshService;
    
    /**
     * 执行脚本（异步）
     */
    public CompletableFuture<ScriptExecution> executeScriptAsync(Long serverId, String scriptContent, String username, String password) {
        return CompletableFuture.supplyAsync(() -> executeScript(serverId, scriptContent, username, password));
    }
    
    /**
     * 执行脚本（同步）
     */
    public ScriptExecution executeScript(Long serverId, String scriptContent, String username, String password) {
        // 检查服务器配置是否存在
        ServerConfig serverConfig = serverConfigMapper.selectById(serverId);
        if (serverConfig == null) {
            throw new RuntimeException("服务器配置不存在，ID: " + serverId);
        }
        
        // 创建执行记录
        ScriptExecution execution = new ScriptExecution(serverId, scriptContent);
        
        // 手动生成ID
        Long maxId = scriptExecutionMapper.selectList(null).stream()
                .mapToLong(ScriptExecution::getId)
                .max()
                .orElse(0L);
        execution.setId(maxId + 1);
        
        scriptExecutionMapper.insert(execution);
        
        try {
            logger.info("开始执行脚本，服务器: {}, 执行ID: {}", serverConfig.getName(), execution.getId());
            
            // 执行SSH命令
            SSHService.SSHResult result = sshService.executeCommand(serverConfig, scriptContent, username, password);
            
            // 更新执行记录
            execution.setExecutionTime(result.getExecutionTime());
            
            if (result.isSuccess()) {
                execution.setStatus(ScriptExecution.ExecutionStatus.SUCCESS);
                execution.setExecutionResult(result.getOutput());
                if (!result.getError().trim().isEmpty()) {
                    execution.setErrorMessage(result.getError());
                }
            } else {
                execution.setStatus(ScriptExecution.ExecutionStatus.FAILED);
                execution.setExecutionResult(result.getOutput());
                execution.setErrorMessage(result.getError());
            }
            
            logger.info("脚本执行完成，状态: {}, 执行时间: {}ms", execution.getStatus(), execution.getExecutionTime());
            
        } catch (Exception e) {
            logger.error("脚本执行异常", e);
            execution.setStatus(ScriptExecution.ExecutionStatus.FAILED);
            execution.setErrorMessage("执行异常: " + e.getMessage());
        }
        
        scriptExecutionMapper.updateById(execution);
        return execution;
    }
    
    /**
     * 获取所有执行记录（按时间倒序排列）
     */
    public List<ScriptExecution> getAllExecutions() {
        return scriptExecutionMapper.findAllOrderByCreatedAtDesc();
    }
    
    /**
     * 根据ID获取执行记录
     */
    public Optional<ScriptExecution> getExecutionById(Long id) {
        ScriptExecution execution = scriptExecutionMapper.selectById(id);
        return Optional.ofNullable(execution);
    }
    
    /**
     * 根据服务器ID获取执行记录
     */
    public List<ScriptExecution> getExecutionsByServerId(Long serverId) {
        return scriptExecutionMapper.findByServerIdOrderByCreatedAtDesc(serverId);
    }
    
    /**
     * 获取最近的执行记录
     */
    public List<ScriptExecution> getRecentExecutions(int limit) {
        return scriptExecutionMapper.findRecentExecutions(limit);
    }
    
    /**
     * 根据状态获取执行记录
     */
    public List<ScriptExecution> getExecutionsByStatus(ScriptExecution.ExecutionStatus status) {
        return scriptExecutionMapper.findByStatusOrderByCreatedAtDesc(status.name());
    }
    
    /**
     * 获取指定时间范围内的执行记录
     */
    public List<ScriptExecution> getExecutionsInTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return scriptExecutionMapper.findExecutionsInTimeRange(startTime, endTime);
    }
    
    /**
     * 删除执行记录
     */
    public void deleteExecution(Long id) {
        if (scriptExecutionMapper.selectById(id) == null) {
            throw new RuntimeException("执行记录不存在，ID: " + id);
        }
        scriptExecutionMapper.deleteById(id);
    }
}
