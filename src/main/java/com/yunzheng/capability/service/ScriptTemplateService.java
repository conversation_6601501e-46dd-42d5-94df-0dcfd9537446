package com.yunzheng.capability.service;

import com.yunzheng.capability.entity.ScriptTemplate;
import com.yunzheng.capability.mapper.ScriptTemplateMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Optional;

@Service
public class ScriptTemplateService {
    
    private static final Logger logger = LoggerFactory.getLogger(ScriptTemplateService.class);
    
    @Autowired
    private ScriptTemplateMapper scriptTemplateMapper;
    
    /**
     * 创建模板
     */
    public ScriptTemplate createTemplate(ScriptTemplate template) {
        // 手动生成ID
        Long maxId = scriptTemplateMapper.selectList(null).stream()
                .mapToLong(ScriptTemplate::getId)
                .max()
                .orElse(0L);
        template.setId(maxId + 1);
        
        scriptTemplateMapper.insert(template);
        logger.info("创建脚本模板: {}", template.getName());
        return template;
    }
    
    /**
     * 获取所有模板
     */
    public List<ScriptTemplate> getAllTemplates() {
        return scriptTemplateMapper.selectList(null);
    }
    
    /**
     * 根据ID获取模板
     */
    public Optional<ScriptTemplate> getTemplateById(Long id) {
        ScriptTemplate template = scriptTemplateMapper.selectById(id);
        return Optional.ofNullable(template);
    }
    
    /**
     * 根据分类获取模板
     */
    public List<ScriptTemplate> getTemplatesByCategory(String category) {
        return scriptTemplateMapper.findByCategory(category);
    }
    
    /**
     * 获取公开模板
     */
    public List<ScriptTemplate> getPublicTemplates() {
        return scriptTemplateMapper.findPublicTemplates();
    }
    
    /**
     * 根据创建者获取模板
     */
    public List<ScriptTemplate> getTemplatesByCreatedBy(String createdBy) {
        return scriptTemplateMapper.findByCreatedBy(createdBy);
    }
    
    /**
     * 搜索模板
     */
    public List<ScriptTemplate> searchTemplates(String keyword) {
        return scriptTemplateMapper.searchTemplates(keyword);
    }
    
    /**
     * 更新模板
     */
    public ScriptTemplate updateTemplate(ScriptTemplate template) {
        if (scriptTemplateMapper.selectById(template.getId()) == null) {
            throw new RuntimeException("模板不存在，ID: " + template.getId());
        }
        
        scriptTemplateMapper.updateById(template);
        logger.info("更新脚本模板: {}", template.getName());
        return template;
    }
    
    /**
     * 删除模板
     */
    public void deleteTemplate(Long id) {
        if (scriptTemplateMapper.selectById(id) == null) {
            throw new RuntimeException("模板不存在，ID: " + id);
        }
        
        scriptTemplateMapper.deleteById(id);
        logger.info("删除脚本模板，ID: {}", id);
    }
    
    /**
     * 获取所有分类
     */
    public List<String> getAllCategories() {
        return scriptTemplateMapper.selectList(null).stream()
                .map(ScriptTemplate::getCategory)
                .distinct()
                .sorted()
                .toList();
    }
}
