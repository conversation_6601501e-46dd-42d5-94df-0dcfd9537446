package com.yunzheng.capability.service;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 图标提取服务
 * 从网站URL中提取favicon图标
 */
@Service
public class IconExtractionService {
    
    private static final Logger logger = LoggerFactory.getLogger(IconExtractionService.class);
    
    /**
     * 从URL提取图标
     * @param url 网站URL
     * @return 图标信息对象
     */
    public IconInfo extractIcon(String url) {
        try {
            // 标准化URL
            String normalizedUrl = normalizeUrl(url);
            logger.info("开始提取图标，URL: {}", normalizedUrl);
            
            // 尝试多种方式获取图标
            IconInfo iconInfo = tryExtractIcon(normalizedUrl);
            
            if (iconInfo != null) {
                logger.info("成功提取图标: {}", iconInfo.getIconUrl());
                return iconInfo;
            } else {
                logger.warn("未能提取到图标: {}", normalizedUrl);
                return createDefaultIcon(normalizedUrl);
            }
            
        } catch (Exception e) {
            logger.error("提取图标时发生错误: {}", e.getMessage(), e);
            return createDefaultIcon(url);
        }
    }
    
    /**
     * 尝试提取图标
     */
    private IconInfo tryExtractIcon(String url) {
        // 1. 尝试从HTML页面解析favicon链接
        IconInfo htmlIcon = extractFromHtml(url);
        if (htmlIcon != null) {
            return htmlIcon;
        }
        
        // 2. 尝试默认favicon.ico路径
        IconInfo defaultIcon = tryDefaultFavicon(url);
        if (defaultIcon != null) {
            return defaultIcon;
        }
        
        return null;
    }
    
    /**
     * 从HTML页面提取favicon
     */
    private IconInfo extractFromHtml(String url) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            return httpClient.execute(request, response -> {
                if (response.getCode() == 200) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        String html = EntityUtils.toString(entity);
                        return parseIconFromHtml(html, url);
                    }
                }
                return null;
            });
            
        } catch (Exception e) {
            logger.warn("从HTML提取图标失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析HTML中的图标链接
     */
    private IconInfo parseIconFromHtml(String html, String baseUrl) {
        try {
            Document doc = Jsoup.parse(html);
            
            // 查找各种类型的图标链接
            List<String> selectors = List.of(
                "link[rel~=(?i)^(shortcut )?icon]",
                "link[rel~=(?i)^apple-touch-icon]",
                "link[rel~=(?i)^mask-icon]",
                "meta[property=og:image]"
            );
            
            for (String selector : selectors) {
                Elements elements = doc.select(selector);
                for (Element element : elements) {
                    String iconUrl = element.attr("href");
                    if (iconUrl.isEmpty()) {
                        iconUrl = element.attr("content"); // for meta tags
                    }
                    
                    if (!iconUrl.isEmpty()) {
                        String fullIconUrl = resolveUrl(baseUrl, iconUrl);
                        if (isValidIconUrl(fullIconUrl)) {
                            return new IconInfo(fullIconUrl, "extracted");
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.warn("解析HTML图标失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 尝试默认favicon.ico路径
     */
    private IconInfo tryDefaultFavicon(String url) {
        try {
            URL urlObj = new URL(url);
            String faviconUrl = urlObj.getProtocol() + "://" + urlObj.getHost() + 
                              (urlObj.getPort() != -1 ? ":" + urlObj.getPort() : "") + "/favicon.ico";
            
            if (isValidIconUrl(faviconUrl)) {
                return new IconInfo(faviconUrl, "default");
            }
            
        } catch (Exception e) {
            logger.warn("尝试默认favicon失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 验证图标URL是否有效
     */
    private boolean isValidIconUrl(String iconUrl) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(iconUrl);
            request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            return httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                if (statusCode == 200) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        String contentType = entity.getContentType();
                        return contentType != null && 
                               (contentType.startsWith("image/") || contentType.contains("icon"));
                    }
                }
                return false;
            });
            
        } catch (Exception e) {
            logger.debug("验证图标URL失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 标准化URL
     */
    private String normalizeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        url = url.trim();
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        
        return url;
    }
    
    /**
     * 解析相对URL为绝对URL
     */
    private String resolveUrl(String baseUrl, String relativeUrl) {
        try {
            if (relativeUrl.startsWith("http://") || relativeUrl.startsWith("https://")) {
                return relativeUrl;
            }
            
            URL base = new URL(baseUrl);
            URL resolved = new URL(base, relativeUrl);
            return resolved.toString();
            
        } catch (MalformedURLException e) {
            logger.warn("解析URL失败: {} + {}", baseUrl, relativeUrl);
            return relativeUrl;
        }
    }
    
    /**
     * 创建默认图标信息
     */
    private IconInfo createDefaultIcon(String url) {
        try {
            URL urlObj = new URL(normalizeUrl(url));
            String domain = urlObj.getHost();
            
            // 使用第三方服务生成默认图标
            String defaultIconUrl = "https://www.google.com/s2/favicons?domain=" + domain + "&sz=64";
            
            return new IconInfo(defaultIconUrl, "google_service");
            
        } catch (Exception e) {
            // 如果所有方法都失败，返回一个通用图标
            return new IconInfo("data:image/svg+xml;base64," + getDefaultSvgIcon(), "fallback");
        }
    }
    
    /**
     * 获取默认SVG图标的Base64编码
     */
    private String getDefaultSvgIcon() {
        String svg = "<svg xmlns='http://www.w3.org/2000/svg' width='64' height='64' viewBox='0 0 24 24' fill='#666'>" +
                    "<path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/>" +
                    "</svg>";
        return Base64.getEncoder().encodeToString(svg.getBytes());
    }
    
    /**
     * 图标信息类
     */
    public static class IconInfo {
        private String iconUrl;
        private String source; // extracted, default, google_service, fallback
        
        public IconInfo(String iconUrl, String source) {
            this.iconUrl = iconUrl;
            this.source = source;
        }
        
        public String getIconUrl() {
            return iconUrl;
        }
        
        public void setIconUrl(String iconUrl) {
            this.iconUrl = iconUrl;
        }
        
        public String getSource() {
            return source;
        }
        
        public void setSource(String source) {
            this.source = source;
        }
    }
}