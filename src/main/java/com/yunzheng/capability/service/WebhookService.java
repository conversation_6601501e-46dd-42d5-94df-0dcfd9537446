package com.yunzheng.capability.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yunzheng.capability.entity.WebhookConfig;
import com.yunzheng.capability.entity.WebhookLog;
import com.yunzheng.capability.mapper.WebhookConfigMapper;
import com.yunzheng.capability.mapper.WebhookLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Webhook处理服务
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class WebhookService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebhookService.class);
    
    @Autowired
    private WebhookLogMapper webhookLogMapper;
    
    @Autowired
    private WebhookConfigMapper webhookConfigMapper;
    
    @Autowired
    private FeishuService feishuService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 处理接收到的Webhook数据
     */
    public WebhookLog processWebhook(String source, Map<String, Object> jsonData) {
        WebhookLog log = new WebhookLog();
        log.setSource(source);
        log.setPushStatus("PENDING");
        
        try {
            // 将JSON数据转换为字符串存储
            String jsonString = objectMapper.writeValueAsString(jsonData);
            log.setJsonData(jsonString);
            
            // 保存日志记录
            webhookLogMapper.insert(log);
            
            // 异步推送到飞书
            pushToFeishuAsync(log, jsonData);
            
            return log;
            
        } catch (Exception e) {
            logger.error("处理Webhook数据失败", e);
            log.setPushStatus("FAILED");
            log.setErrorMessage(e.getMessage());
            webhookLogMapper.updateById(log);
            throw new RuntimeException("处理Webhook数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步推送到飞书
     */
    private void pushToFeishuAsync(WebhookLog log, Map<String, Object> jsonData) {
        // 这里可以使用@Async注解或者线程池来实现异步处理
        // 为了简化，这里先同步处理
        try {
            // 获取启用的飞书配置
            List<WebhookConfig> configs = getEnabledConfigs();
            
            if (configs.isEmpty()) {
                log.setPushStatus("FAILED");
                log.setErrorMessage("没有找到启用的飞书配置");
                log.setUpdatedAt(LocalDateTime.now());
                webhookLogMapper.updateById(log);
                return;
            }
            
            // 格式化消息
            String message = formatMessage(log.getSource(), jsonData);
            
            // 推送到所有启用的飞书配置
            StringBuilder results = new StringBuilder();
            boolean hasSuccess = false;
            
            for (WebhookConfig config : configs) {
                try {
                    String result = feishuService.pushToFeishu(config.getFeishuWebhookUrl(), message);
                    results.append(String.format("[%s]: %s\n", config.getName(), result));
                    hasSuccess = true;
                } catch (Exception e) {
                    results.append(String.format("[%s]: 推送失败 - %s\n", config.getName(), e.getMessage()));
                }
            }
            
            // 更新日志状态
            log.setPushStatus(hasSuccess ? "SUCCESS" : "FAILED");
            log.setPushResult(results.toString());
            log.setUpdatedAt(LocalDateTime.now());
            webhookLogMapper.updateById(log);
            
        } catch (Exception e) {
            logger.error("推送到飞书失败", e);
            log.setPushStatus("FAILED");
            log.setErrorMessage(e.getMessage());
            log.setUpdatedAt(LocalDateTime.now());
            webhookLogMapper.updateById(log);
        }
    }
    
    /**
     * 格式化消息内容
     */
    private String formatMessage(String source, Map<String, Object> jsonData) {
        if ("gitlab".equalsIgnoreCase(source)) {
            return feishuService.formatGitLabMessage(jsonData);
        }
        
        // 默认格式化
        try {
            String formattedJson = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(jsonData);
            return String.format("收到来自 %s 的Webhook数据:\n```\n%s\n```", source, formattedJson);
        } catch (Exception e) {
            return String.format("收到来自 %s 的Webhook数据，但无法格式化: %s", source, e.getMessage());
        }
    }
    
    /**
     * 获取启用的飞书配置
     */
    private List<WebhookConfig> getEnabledConfigs() {
        QueryWrapper<WebhookConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enabled", true);
        return webhookConfigMapper.selectList(queryWrapper);
    }
    
    /**
     * 分页查询Webhook日志
     */
    public Page<WebhookLog> getWebhookLogs(int page, int size) {
        Page<WebhookLog> pageRequest = new Page<>(page, size);
        QueryWrapper<WebhookLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_at");
        return webhookLogMapper.selectPage(pageRequest, queryWrapper);
    }
    
    /**
     * 根据来源查询Webhook日志
     */
    public List<WebhookLog> getWebhookLogsBySource(String source) {
        QueryWrapper<WebhookLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source", source).orderByDesc("created_at");
        return webhookLogMapper.selectList(queryWrapper);
    }
    
    /**
     * 保存Webhook配置
     */
    public WebhookConfig saveWebhookConfig(WebhookConfig config) {
        config.setUpdatedAt(LocalDateTime.now());
        if (config.getId() == null) {
            webhookConfigMapper.insert(config);
        } else {
            webhookConfigMapper.updateById(config);
        }
        return config;
    }
    
    /**
     * 获取所有Webhook配置
     */
    public List<WebhookConfig> getAllWebhookConfigs() {
        return webhookConfigMapper.selectList(null);
    }
    
    /**
     * 删除Webhook配置
     */
    public void deleteWebhookConfig(Long id) {
        webhookConfigMapper.deleteById(id);
    }
    
    /**
     * 重新推送失败的消息
     */
    public void retryFailedPush(Long logId) {
        WebhookLog log = webhookLogMapper.selectById(logId);
        if (log == null) {
            throw new RuntimeException("找不到指定的Webhook日志");
        }
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> jsonData = objectMapper.readValue(log.getJsonData(), Map.class);
            pushToFeishuAsync(log, jsonData);
        } catch (Exception e) {
            logger.error("重新推送失败", e);
            throw new RuntimeException("重新推送失败: " + e.getMessage(), e);
        }
    }
}
