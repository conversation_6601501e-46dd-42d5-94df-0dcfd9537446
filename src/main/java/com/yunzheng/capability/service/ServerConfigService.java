package com.yunzheng.capability.service;

import com.yunzheng.capability.entity.ServerConfig;
import com.yunzheng.capability.mapper.ServerConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ServerConfigService {
    
    @Autowired
    private ServerConfigMapper serverConfigMapper;
    
    /**
     * 获取所有服务器配置
     */
    public List<ServerConfig> getAllServers() {
        return serverConfigMapper.selectList(null);
    }
    
    /**
     * 根据ID获取服务器配置
     */
    public Optional<ServerConfig> getServerById(Long id) {
        ServerConfig server = serverConfigMapper.selectById(id);
        return Optional.ofNullable(server);
    }
    
    /**
     * 创建新的服务器配置
     */
    public ServerConfig createServer(ServerConfig serverConfig) {
        // 检查服务器名称是否已存在
        if (serverConfigMapper.countByName(serverConfig.getName()) > 0) {
            throw new RuntimeException("服务器名称已存在: " + serverConfig.getName());
        }
        
        // 使用更安全的ID生成方式
        Long maxId = serverConfigMapper.selectList(null).stream()
                .mapToLong(ServerConfig::getId)
                .max()
                .orElse(0L);
        serverConfig.setId(maxId + 1);
        
        // 确保端口有默认值
        if (serverConfig.getPort() == null) {
            serverConfig.setPort(22);
        }
        
        int result = serverConfigMapper.insert(serverConfig);
        if (result <= 0) {
            throw new RuntimeException("创建服务器配置失败");
        }
        
        return serverConfig;
    }
    
    /**
     * 更新服务器配置
     */
    public ServerConfig updateServer(Long id, ServerConfig serverConfig) {
        ServerConfig existing = serverConfigMapper.selectById(id);
        if (existing == null) {
            throw new RuntimeException("服务器配置不存在，ID: " + id);
        }
        
        // 检查名称是否与其他服务器冲突
        if (!existing.getName().equals(serverConfig.getName()) && 
            serverConfigMapper.countByName(serverConfig.getName()) > 0) {
            throw new RuntimeException("服务器名称已存在: " + serverConfig.getName());
        }
        
        existing.setName(serverConfig.getName());
        existing.setHost(serverConfig.getHost());
        existing.setPort(serverConfig.getPort() != null ? serverConfig.getPort() : 22);
        existing.setDescription(serverConfig.getDescription());
        
        int result = serverConfigMapper.updateById(existing);
        if (result <= 0) {
            throw new RuntimeException("更新服务器配置失败");
        }
        
        return existing;
    }
    
    /**
     * 删除服务器配置
     */
    public void deleteServer(Long id) {
        if (serverConfigMapper.selectById(id) == null) {
            throw new RuntimeException("服务器配置不存在，ID: " + id);
        }
        int result = serverConfigMapper.deleteById(id);
        if (result <= 0) {
            throw new RuntimeException("删除服务器配置失败");
        }
    }
    
    /**
     * 根据名称搜索服务器
     */
    public List<ServerConfig> searchServersByName(String name) {
        return serverConfigMapper.findByNameLike(name);
    }
    
    /**
     * 测试服务器连接
     */
    public boolean testConnection(Long id) {
        ServerConfig serverConfig = serverConfigMapper.selectById(id);
        if (serverConfig == null) {
            throw new RuntimeException("服务器配置不存在，ID: " + id);
        }
        
        // 这里可以实现实际的连接测试逻辑
        // 暂时返回true作为示例
        return true;
    }
}
