package com.yunzheng.capability.service;

import com.jcraft.jsch.*;
import com.yunzheng.capability.entity.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;

@Service
public class SSHService {
    
    private static final Logger logger = LoggerFactory.getLogger(SSHService.class);
    
    /**
     * 执行SSH命令（使用ServerConfig中的密码）
     */
    public SSHResult executeCommand(ServerConfig serverConfig, String command) {
        throw new UnsupportedOperationException("请使用带密码参数的方法");
    }
    
    /**
     * 执行SSH命令（使用传入的用户名和密码）
     */
    public SSHResult executeCommand(ServerConfig serverConfig, String command, String username, String password) {
        JSch jsch = new JSch();
        Session session = null;
        ChannelExec channelExec = null;
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 创建会话
            session = jsch.getSession(username, serverConfig.getHost(), serverConfig.getPort());
            session.setPassword(password);
            
            // 设置会话属性
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            
            // 连接
            session.connect(30000); // 30秒超时
            logger.info("SSH连接成功: {}@{}:{}", username, serverConfig.getHost(), serverConfig.getPort());
            
            // 创建执行通道
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);
            
            // 获取输入流和错误流
            InputStream inputStream = channelExec.getInputStream();
            InputStream errorStream = channelExec.getErrStream();
            
            // 执行命令
            channelExec.connect();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            StringBuilder error = new StringBuilder();
            
            // 读取标准输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            
            // 读取错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream));
            while ((line = errorReader.readLine()) != null) {
                error.append(line).append("\n");
            }
            
            // 等待命令执行完成
            while (!channelExec.isClosed()) {
                Thread.sleep(100);
            }
            
            int exitCode = channelExec.getExitStatus();
            long executionTime = System.currentTimeMillis() - startTime;
            
            logger.info("命令执行完成，退出码: {}, 执行时间: {}ms", exitCode, executionTime);
            
            // 简化逻辑：直接返回执行结果，不区分错误
            return new SSHResult(
                exitCode == 0,
                output.toString(),
                error.toString(),
                exitCode,
                (int) executionTime
            );
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("SSH命令执行失败", e);
            return new SSHResult(
                false,
                "",
                "连接或执行失败: " + e.getMessage(),
                -1,
                (int) executionTime
            );
        } finally {
            // 关闭连接
            if (channelExec != null && channelExec.isConnected()) {
                channelExec.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }
    
    /**
     * 测试SSH连接（使用ServerConfig中的密码）
     */
    public boolean testConnection(ServerConfig serverConfig) {
        throw new UnsupportedOperationException("请使用带密码参数的方法");
    }
    
    /**
     * 测试SSH连接（使用传入的用户名和密码）
     */
    public boolean testConnection(ServerConfig serverConfig, String username, String password) {
        JSch jsch = new JSch();
        Session session = null;
        
        try {
            session = jsch.getSession(username, serverConfig.getHost(), serverConfig.getPort());
            session.setPassword(password);
            
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            
            session.connect(10000); // 10秒超时
            logger.info("SSH连接测试成功: {}@{}:{}", username, serverConfig.getHost(), serverConfig.getPort());
            return true;
            
        } catch (Exception e) {
            logger.error("SSH连接测试失败", e);
            return false;
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }
    

    
    /**
     * SSH执行结果
     */
    public static class SSHResult {
        private final boolean success;
        private final String output;
        private final String error;
        private final int exitCode;
        private final int executionTime;
        
        public SSHResult(boolean success, String output, String error, int exitCode, int executionTime) {
            this.success = success;
            this.output = output;
            this.error = error;
            this.exitCode = exitCode;
            this.executionTime = executionTime;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getOutput() {
            return output;
        }
        
        public String getError() {
            return error;
        }
        
        public int getExitCode() {
            return exitCode;
        }
        
        public int getExecutionTime() {
            return executionTime;
        }
    }
}
