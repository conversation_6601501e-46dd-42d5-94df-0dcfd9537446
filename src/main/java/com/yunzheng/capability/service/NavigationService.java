package com.yunzheng.capability.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunzheng.capability.entity.Navigation;
import com.yunzheng.capability.mapper.NavigationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class NavigationService {
    
    @Autowired
    private NavigationMapper navigationMapper;
    
    /**
     * 创建导航链接
     * @param navigation 导航链接对象
     * @return 创建结果
     */
    public boolean createNavigation(Navigation navigation) {
        // 如果没有设置排序，设置为最大值+1
        if (navigation.getSort() == null) {
            QueryWrapper<Navigation> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("sort").last("LIMIT 1");
            Navigation lastNavigation = navigationMapper.selectOne(queryWrapper);
            int nextSort = (lastNavigation != null && lastNavigation.getSort() != null) ? 
                          lastNavigation.getSort() + 1 : 1;
            navigation.setSort(nextSort);
        }
        return navigationMapper.insert(navigation) > 0;
    }
    
    /**
     * 更新导航链接
     * @param navigation 导航链接对象
     * @return 更新结果
     */
    public boolean updateNavigation(Navigation navigation) {
        return navigationMapper.updateById(navigation) > 0;
    }
    
    /**
     * 删除导航链接
     * @param id 导航链接ID
     * @return 删除结果
     */
    public boolean deleteNavigation(Long id) {
        return navigationMapper.deleteById(id) > 0;
    }
    
    /**
     * 根据ID获取导航链接
     * @param id 导航链接ID
     * @return 导航链接对象
     */
    public Navigation getNavigationById(Long id) {
        return navigationMapper.selectById(id);
    }
    
    /**
     * 分页查询导航链接
     * @param page 页码
     * @param size 每页大小
     * @param category 分类（可选）
     * @param keyword 搜索关键词（可选）
     * @return 分页结果
     */
    public Page<Navigation> getNavigationPage(int page, int size, String category, String keyword) {
        Page<Navigation> pageObj = new Page<>(page, size);
        QueryWrapper<Navigation> queryWrapper = new QueryWrapper<>();
        
        // 分类筛选
        if (StringUtils.hasText(category)) {
            queryWrapper.eq("category", category);
        }
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("description", keyword)
                .or()
                .like("url", keyword)
            );
        }
        
        // 排序
        queryWrapper.orderByAsc("sort").orderByDesc("created_at");
        
        return navigationMapper.selectPage(pageObj, queryWrapper);
    }
    
    /**
     * 获取所有导航链接（按排序）
     * @return 导航链接列表
     */
    public List<Navigation> getAllNavigations() {
        return navigationMapper.selectAllOrderBySort();
    }
    
    /**
     * 根据分类获取导航链接
     * @param category 分类
     * @return 导航链接列表
     */
    public List<Navigation> getNavigationsByCategory(String category) {
        return navigationMapper.selectByCategory(category);
    }
    
    /**
     * 根据名称搜索导航链接
     * @param name 名称关键词
     * @return 导航链接列表
     */
    public List<Navigation> searchNavigationsByName(String name) {
        return navigationMapper.selectByNameLike(name);
    }
    
    /**
     * 获取所有分类
     * @return 分类列表
     */
    public List<String> getAllCategories() {
        return navigationMapper.selectAllCategories();
    }
    
    /**
     * 批量删除导航链接
     * @param ids 导航链接ID列表
     * @return 删除结果
     */
    public boolean batchDeleteNavigations(List<Long> ids) {
        return navigationMapper.deleteBatchIds(ids) > 0;
    }
}