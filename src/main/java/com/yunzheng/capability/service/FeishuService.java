package com.yunzheng.capability.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 飞书推送服务
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class FeishuService {
    
    private static final Logger logger = LoggerFactory.getLogger(FeishuService.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 推送消息到飞书
     */
    public String pushToFeishu(String webhookUrl, String message) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(webhookUrl);
            
            // 构建飞书消息格式
            Map<String, Object> messageBody = new HashMap<>();
            messageBody.put("msg_type", "text");
            
            Map<String, String> content = new HashMap<>();
            content.put("text", message);
            messageBody.put("content", content);
            
            String jsonBody = objectMapper.writeValueAsString(messageBody);
            post.setEntity(new StringEntity(jsonBody, ContentType.APPLICATION_JSON));
            
            var response = httpClient.execute(post);
            String responseBody = new String(response.getEntity().getContent().readAllBytes());
            
            logger.info("飞书推送响应: {}", responseBody);
            return responseBody;
            
        } catch (Exception e) {
            logger.error("推送到飞书失败", e);
            throw new RuntimeException("推送到飞书失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 推送富文本消息到飞书
     */
    public String pushRichTextToFeishu(String webhookUrl, String title, String content) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(webhookUrl);
            
            // 构建飞书富文本消息格式
            Map<String, Object> messageBody = new HashMap<>();
            messageBody.put("msg_type", "post");
            
            Map<String, Object> postContent = new HashMap<>();
            Map<String, Object> zhCn = new HashMap<>();
            zhCn.put("title", title);
            
            // 构建富文本内容
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("tag", "text");
            textContent.put("text", content);
            
            @SuppressWarnings("unchecked")
            Map<String, Object>[][] contentArray = new Map[][] {
                { textContent }
            };
            zhCn.put("content", contentArray);
            postContent.put("zh_cn", zhCn);
            messageBody.put("content", postContent);
            
            String jsonBody = objectMapper.writeValueAsString(messageBody);
            post.setEntity(new StringEntity(jsonBody, ContentType.APPLICATION_JSON));
            
            var response = httpClient.execute(post);
            String responseBody = new String(response.getEntity().getContent().readAllBytes());
            
            logger.info("飞书富文本推送响应: {}", responseBody);
            return responseBody;
            
        } catch (Exception e) {
            logger.error("推送富文本到飞书失败", e);
            throw new RuntimeException("推送富文本到飞书失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 格式化GitLab Webhook数据为飞书消息
     */
    public String formatGitLabMessage(Map<String, Object> webhookData) {
        try {
            String objectKind = (String) webhookData.get("object_kind");
            
            if ("pipeline".equals(objectKind)) {
                return formatPipelineMessage(webhookData);
            } else if ("push".equals(objectKind)) {
                return formatPushMessage(webhookData);
            } else if ("merge_request".equals(objectKind)) {
                return formatMergeRequestMessage(webhookData);
            }
            
            // 默认格式化
            return formatDefaultMessage(webhookData);
            
        } catch (Exception e) {
            logger.error("格式化GitLab消息失败", e);
            return "收到GitLab Webhook数据，但格式化失败: " + e.getMessage();
        }
    }
    
    /**
     * 格式化Pipeline消息
     */
    @SuppressWarnings("unchecked")
    private String formatPipelineMessage(Map<String, Object> webhookData) {
        Map<String, Object> objectAttributes = (Map<String, Object>) webhookData.get("object_attributes");
        Map<String, Object> project = (Map<String, Object>) webhookData.get("project");
        Map<String, Object> user = (Map<String, Object>) webhookData.get("user");
        
        String projectName = (String) project.get("name");
        String userName = (String) user.get("name");
        String status = (String) objectAttributes.get("status");
        String ref = (String) objectAttributes.get("ref");
        String pipelineUrl = (String) objectAttributes.get("url");
        
        return String.format(
            "🚀 GitLab Pipeline 通知\n" +
            "项目: %s\n" +
            "分支: %s\n" +
            "状态: %s\n" +
            "触发人: %s\n" +
            "详情: %s",
            projectName, ref, status, userName, pipelineUrl
        );
    }
    
    /**
     * 格式化Push消息
     */
    @SuppressWarnings("unchecked")
    private String formatPushMessage(Map<String, Object> webhookData) {
        Map<String, Object> project = (Map<String, Object>) webhookData.get("project");
        String projectName = (String) project.get("name");
        String ref = (String) webhookData.get("ref");
        String userName = (String) webhookData.get("user_name");
        
        return String.format(
            "📝 GitLab Push 通知\n" +
            "项目: %s\n" +
            "分支: %s\n" +
            "推送人: %s",
            projectName, ref, userName
        );
    }
    
    /**
     * 格式化Merge Request消息
     */
    @SuppressWarnings("unchecked")
    private String formatMergeRequestMessage(Map<String, Object> webhookData) {
        Map<String, Object> objectAttributes = (Map<String, Object>) webhookData.get("object_attributes");
        Map<String, Object> project = (Map<String, Object>) webhookData.get("project");
        Map<String, Object> user = (Map<String, Object>) webhookData.get("user");
        
        String projectName = (String) project.get("name");
        String title = (String) objectAttributes.get("title");
        String action = (String) objectAttributes.get("action");
        String userName = (String) user.get("name");
        String mergeRequestUrl = (String) objectAttributes.get("url");
        
        return String.format(
            "🔄 GitLab Merge Request 通知\n" +
            "项目: %s\n" +
            "标题: %s\n" +
            "动作: %s\n" +
            "操作人: %s\n" +
            "详情: %s",
            projectName, title, action, userName, mergeRequestUrl
        );
    }
    
    /**
     * 默认格式化消息
     */
    private String formatDefaultMessage(Map<String, Object> webhookData) {
        try {
            String formattedJson = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(webhookData);
            return "收到Webhook数据:\n```\n" + formattedJson + "\n```";
        } catch (Exception e) {
            return "收到Webhook数据，但无法格式化: " + e.getMessage();
        }
    }
}
