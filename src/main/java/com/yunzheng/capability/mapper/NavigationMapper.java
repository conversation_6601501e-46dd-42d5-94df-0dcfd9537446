package com.yunzheng.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunzheng.capability.entity.Navigation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface NavigationMapper extends BaseMapper<Navigation> {
    
    /**
     * 根据分类查询导航链接
     * @param category 分类
     * @return 导航链接列表
     */
    @Select("SELECT * FROM navigation WHERE category = #{category} ORDER BY sort ASC, created_at DESC")
    List<Navigation> selectByCategory(String category);
    
    /**
     * 根据名称模糊查询导航链接
     * @param name 名称关键词
     * @return 导航链接列表
     */
    @Select("SELECT * FROM navigation WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY sort ASC, created_at DESC")
    List<Navigation> selectByNameLike(String name);
    
    /**
     * 获取所有分类
     * @return 分类列表
     */
    @Select("SELECT DISTINCT category FROM navigation WHERE category IS NOT NULL AND category != '' ORDER BY category")
    List<String> selectAllCategories();
    
    /**
     * 获取排序后的所有导航链接
     * @return 导航链接列表
     */
    @Select("SELECT * FROM navigation ORDER BY sort ASC, created_at DESC")
    List<Navigation> selectAllOrderBySort();
}