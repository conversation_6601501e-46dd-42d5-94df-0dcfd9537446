package com.yunzheng.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunzheng.capability.entity.ScriptExecution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ScriptExecutionMapper extends BaseMapper<ScriptExecution> {
    
    /**
     * 根据服务器ID查找执行记录
     */
    @Select("SELECT * FROM script_execution WHERE server_id = #{serverId} ORDER BY created_at DESC")
    List<ScriptExecution> findByServerIdOrderByCreatedAtDesc(Long serverId);
    
    /**
     * 根据执行状态查找记录
     */
    @Select("SELECT * FROM script_execution WHERE status = #{status} ORDER BY created_at DESC")
    List<ScriptExecution> findByStatusOrderByCreatedAtDesc(String status);
    
    /**
     * 查找指定时间范围内的执行记录
     */
    @Select("SELECT * FROM script_execution WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<ScriptExecution> findExecutionsInTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最近的执行记录
     */
    @Select("SELECT * FROM script_execution ORDER BY created_at DESC LIMIT #{limit}")
    List<ScriptExecution> findRecentExecutions(int limit);
    
    /**
     * 获取所有执行记录（按时间倒序排列）
     */
    @Select("SELECT * FROM script_execution ORDER BY created_at DESC")
    List<ScriptExecution> findAllOrderByCreatedAtDesc();
}
