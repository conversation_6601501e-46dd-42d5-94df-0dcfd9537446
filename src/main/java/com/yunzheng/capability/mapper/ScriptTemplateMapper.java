package com.yunzheng.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunzheng.capability.entity.ScriptTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ScriptTemplateMapper extends BaseMapper<ScriptTemplate> {
    
    /**
     * 根据分类查找模板
     */
    @Select("SELECT * FROM script_template WHERE category = #{category} ORDER BY created_at DESC")
    List<ScriptTemplate> findByCategory(String category);
    
    /**
     * 查找公开模板
     */
    @Select("SELECT * FROM script_template WHERE is_public = 1 ORDER BY created_at DESC")
    List<ScriptTemplate> findPublicTemplates();
    
    /**
     * 根据创建者查找模板
     */
    @Select("SELECT * FROM script_template WHERE created_by = #{createdBy} ORDER BY created_at DESC")
    List<ScriptTemplate> findByCreatedBy(String createdBy);
    
    /**
     * 搜索模板（按名称和描述）
     */
    @Select("SELECT * FROM script_template WHERE (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) ORDER BY created_at DESC")
    List<ScriptTemplate> searchTemplates(String keyword);
}
