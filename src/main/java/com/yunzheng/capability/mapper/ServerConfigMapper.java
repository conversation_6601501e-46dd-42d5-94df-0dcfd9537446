package com.yunzheng.capability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunzheng.capability.entity.ServerConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ServerConfigMapper extends BaseMapper<ServerConfig> {
    
    /**
     * 根据服务器名称模糊查询
     */
    @Select("SELECT * FROM server_config WHERE name LIKE CONCAT('%', #{name}, '%')")
    List<ServerConfig> findByNameLike(String name);
    
    /**
     * 根据主机地址查找配置
     */
    @Select("SELECT * FROM server_config WHERE host = #{host}")
    List<ServerConfig> findByHost(String host);
    
    /**
     * 检查服务器名称是否已存在
     */
    @Select("SELECT COUNT(*) FROM server_config WHERE name = #{name}")
    int countByName(String name);
}
