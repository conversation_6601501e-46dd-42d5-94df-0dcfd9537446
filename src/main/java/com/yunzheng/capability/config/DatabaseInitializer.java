package com.yunzheng.capability.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.stream.Collectors;

/**
 * 数据库初始化检查器
 * 在应用启动时检查数据库是否需要初始化
 */
@Component
public class DatabaseInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializer.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        logger.info("检查数据库初始化状态...");
        
        if (isDatabaseInitialized()) {
            logger.info("数据库已初始化，跳过初始化步骤");
        } else {
            logger.info("数据库需要初始化，开始执行初始化脚本");
            initializeDatabase();
        }
    }

    /**
     * 检查数据库是否已初始化
     * 通过检查是否存在特定表来判断
     */
    private boolean isDatabaseInitialized() {
        try (Connection conn = dataSource.getConnection()) {
            // 检查是否存在server_config表
            try (ResultSet tables = conn.getMetaData().getTables(
                    null, null, "server_config", new String[]{"TABLE"})) {
                
                if (tables.next()) {
                    // 表存在，检查是否有数据
                    Integer count = jdbcTemplate.queryForObject(
                            "SELECT COUNT(*) FROM server_config", Integer.class);
                    return count != null && count > 0;
                }
                return false;
            }
        } catch (SQLException e) {
            logger.error("检查数据库初始化状态时出错", e);
            return false;
        }
    }

    /**
     * 初始化数据库
     * 执行schema-mysql.sql脚本
     */
    private void initializeDatabase() {
        try {
            // 读取SQL脚本文件
            ClassPathResource resource = new ClassPathResource("schema-mysql.sql");
            String sql = new BufferedReader(
                    new InputStreamReader(resource.getInputStream()))
                    .lines()
                    .collect(Collectors.joining("\n"));

            // 执行SQL脚本
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement()) {
                
                logger.info("开始执行数据库初始化脚本");
                stmt.execute(sql);
                logger.info("数据库初始化完成");
            }
        } catch (Exception e) {
            logger.error("初始化数据库时出错", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }
}