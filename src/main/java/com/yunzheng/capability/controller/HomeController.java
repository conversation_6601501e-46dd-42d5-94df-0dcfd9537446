package com.yunzheng.capability.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import com.yunzheng.capability.service.ServerConfigService;
import com.yunzheng.capability.service.ScriptTemplateService;
import java.util.HashMap;
import java.util.Map;

@RestController
public class HomeController {
    
    @Autowired
    private ServerConfigService serverConfigService;
    
    @Autowired
    private ScriptTemplateService scriptTemplateService;
    
    /**
     * 根路径健康检查
     */
    @GetMapping("/")
    public ResponseEntity<String> home() {
        return ResponseEntity.ok("YZ-CI Service is running");
    }
    
    /**
     * 简单健康检查端点
     */
    @GetMapping("/health/simple")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("OK");
    }
    
    /**
     * 详细健康检查
     */
    @GetMapping("/health/detailed")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        
        try {
            // 测试数据库连接
            serverConfigService.getAllServers();
            health.put("database", "UP");
        } catch (Exception e) {
            health.put("database", "DOWN");
            health.put("databaseError", e.getMessage());
        }
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * 测试服务器API
     */
    @GetMapping("/test/servers")
    public ResponseEntity<?> testServers() {
        try {
            var servers = serverConfigService.getAllServers();
            return ResponseEntity.ok(servers);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }
    
    /**
     * 测试模板API
     */
    @GetMapping("/test/templates")
    public ResponseEntity<?> testTemplates() {
        try {
            var templates = scriptTemplateService.getAllTemplates();
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }
}
