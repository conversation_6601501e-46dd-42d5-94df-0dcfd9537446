package com.yunzheng.capability.controller;

import com.yunzheng.capability.entity.ScriptExecution;
import com.yunzheng.capability.service.ScriptExecutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.Map;

@RestController
@RequestMapping("/api/executions")
public class ScriptExecutionController {
    
    @Autowired
    private ScriptExecutionService scriptExecutionService;
    
    /**
     * 执行脚本
     */
    @PostMapping("/execute")
    public ResponseEntity<?> executeScript(@RequestBody Map<String, Object> request) {
        try {
            Long serverId = Long.valueOf(request.get("serverId").toString());
            String scriptContent = (String) request.get("scriptContent");
            String username = (String) request.get("username");
            String password = (String) request.get("password");
            
            if (serverId == null || scriptContent == null || username == null || password == null) {
                return ResponseEntity.badRequest().body("缺少必要参数");
            }
            
            ScriptExecution execution = scriptExecutionService.executeScript(serverId, scriptContent, username, password);
            return ResponseEntity.ok(execution);
            
        } catch (NumberFormatException e) {
            return ResponseEntity.badRequest().body("服务器ID格式错误");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("执行脚本失败");
        }
    }
    
    /**
     * 获取所有脚本执行记录
     */
    @GetMapping
    public ResponseEntity<List<ScriptExecution>> getAllExecutions() {
        try {
            List<ScriptExecution> executions = scriptExecutionService.getAllExecutions();
            return ResponseEntity.ok(executions);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据ID获取脚本执行记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<ScriptExecution> getExecutionById(@PathVariable Long id) {
        try {
            Optional<ScriptExecution> execution = scriptExecutionService.getExecutionById(id);
            return execution.map(ResponseEntity::ok)
                           .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 删除脚本执行记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteExecution(@PathVariable Long id) {
        try {
            scriptExecutionService.deleteExecution(id);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("删除脚本执行记录失败");
        }
    }
    
    /**
     * 根据服务器ID获取执行记录
     */
    @GetMapping("/server/{serverId}")
    public ResponseEntity<List<ScriptExecution>> getExecutionsByServerId(@PathVariable Long serverId) {
        try {
            List<ScriptExecution> executions = scriptExecutionService.getExecutionsByServerId(serverId);
            return ResponseEntity.ok(executions);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取最近的执行记录
     */
    @GetMapping("/recent")
    public ResponseEntity<List<ScriptExecution>> getRecentExecutions(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<ScriptExecution> executions = scriptExecutionService.getRecentExecutions(limit);
            return ResponseEntity.ok(executions);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据状态获取执行记录
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<ScriptExecution>> getExecutionsByStatus(@PathVariable String status) {
        try {
            ScriptExecution.ExecutionStatus executionStatus = ScriptExecution.ExecutionStatus.valueOf(status.toUpperCase());
            List<ScriptExecution> executions = scriptExecutionService.getExecutionsByStatus(executionStatus);
            return ResponseEntity.ok(executions);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
}
