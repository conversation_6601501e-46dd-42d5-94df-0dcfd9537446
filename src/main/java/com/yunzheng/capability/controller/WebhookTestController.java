package com.yunzheng.capability.controller;

import com.yunzheng.capability.service.WebhookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Webhook测试控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/api/webhook/test")
public class WebhookTestController {
    
    @Autowired
    private WebhookService webhookService;
    
    /**
     * 测试GitLab Pipeline Webhook
     */
    @PostMapping("/gitlab-pipeline")
    public ResponseEntity<Map<String, Object>> testGitLabPipeline() {
        // 模拟GitLab Pipeline Webhook数据
        Map<String, Object> testData = new HashMap<>();
        testData.put("object_kind", "pipeline");
        
        Map<String, Object> objectAttributes = new HashMap<>();
        objectAttributes.put("id", 266);
        objectAttributes.put("status", "success");
        objectAttributes.put("ref", "main");
        objectAttributes.put("url", "https://gitlab.example.com/project/-/pipelines/266");
        testData.put("object_attributes", objectAttributes);
        
        Map<String, Object> project = new HashMap<>();
        project.put("name", "test-project");
        project.put("web_url", "https://gitlab.example.com/project");
        testData.put("project", project);
        
        Map<String, Object> user = new HashMap<>();
        user.put("name", "测试用户");
        user.put("username", "testuser");
        testData.put("user", user);
        
        try {
            var log = webhookService.processWebhook("gitlab", testData);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "测试数据已处理");
            response.put("logId", log.getId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 测试通用Webhook
     */
    @PostMapping("/generic")
    public ResponseEntity<Map<String, Object>> testGeneric() {
        // 模拟通用Webhook数据
        Map<String, Object> testData = new HashMap<>();
        testData.put("event", "test_event");
        testData.put("timestamp", System.currentTimeMillis());
        testData.put("message", "这是一个测试消息");
        
        Map<String, Object> data = new HashMap<>();
        data.put("key1", "value1");
        data.put("key2", "value2");
        testData.put("data", data);
        
        try {
            var log = webhookService.processWebhook("test", testData);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "通用测试数据已处理");
            response.put("logId", log.getId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
