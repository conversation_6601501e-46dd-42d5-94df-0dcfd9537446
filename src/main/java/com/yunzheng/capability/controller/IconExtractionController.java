package com.yunzheng.capability.controller;

import com.yunzheng.capability.service.IconExtractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 图标提取控制器
 * 提供从URL提取图标的API接口
 */
@RestController
@RequestMapping("/api/icons")
@CrossOrigin(origins = "*")
public class IconExtractionController {
    
    @Autowired
    private IconExtractionService iconExtractionService;
    
    /**
     * 从URL提取图标
     * @param request 包含URL的请求对象
     * @return 图标信息
     */
    @PostMapping("/extract")
    public ResponseEntity<Map<String, Object>> extractIcon(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String url = request.get("url");
            
            if (url == null || url.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "URL参数不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 提取图标
            IconExtractionService.IconInfo iconInfo = iconExtractionService.extractIcon(url);
            
            response.put("success", true);
            response.put("message", "图标提取成功");
            response.put("data", Map.of(
                "iconUrl", iconInfo.getIconUrl(),
                "source", iconInfo.getSource()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "提取图标时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量提取图标
     * @param request 包含URL列表的请求对象
     * @return 图标信息列表
     */
    @PostMapping("/extract-batch")
    public ResponseEntity<Map<String, Object>> extractIconsBatch(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            java.util.List<String> urls = (java.util.List<String>) request.get("urls");
            
            if (urls == null || urls.isEmpty()) {
                response.put("success", false);
                response.put("message", "URLs参数不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            java.util.List<Map<String, Object>> results = new java.util.ArrayList<>();
            
            for (String url : urls) {
                try {
                    IconExtractionService.IconInfo iconInfo = iconExtractionService.extractIcon(url);
                    results.add(Map.of(
                        "url", url,
                        "success", true,
                        "iconUrl", iconInfo.getIconUrl(),
                        "source", iconInfo.getSource()
                    ));
                } catch (Exception e) {
                    results.add(Map.of(
                        "url", url,
                        "success", false,
                        "error", e.getMessage()
                    ));
                }
            }
            
            response.put("success", true);
            response.put("message", "批量提取完成");
            response.put("data", results);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量提取图标时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Icon Extraction Service");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
}