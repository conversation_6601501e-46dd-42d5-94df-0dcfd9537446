package com.yunzheng.capability.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunzheng.capability.entity.Navigation;
import com.yunzheng.capability.service.NavigationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/navigations")
@CrossOrigin(origins = "*")
public class NavigationController {
    
    @Autowired
    private NavigationService navigationService;
    
    /**
     * 创建导航链接
     * @param navigation 导航链接对象
     * @return 创建结果
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createNavigation(@RequestBody Navigation navigation) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = navigationService.createNavigation(navigation);
            if (success) {
                response.put("success", true);
                response.put("message", "导航链接创建成功");
                response.put("data", navigation);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "导航链接创建失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建导航链接时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 更新导航链接
     * @param id 导航链接ID
     * @param navigation 导航链接对象
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateNavigation(@PathVariable Long id, @RequestBody Navigation navigation) {
        Map<String, Object> response = new HashMap<>();
        try {
            navigation.setId(id);
            boolean success = navigationService.updateNavigation(navigation);
            if (success) {
                response.put("success", true);
                response.put("message", "导航链接更新成功");
                response.put("data", navigation);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "导航链接更新失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新导航链接时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 删除导航链接
     * @param id 导航链接ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteNavigation(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = navigationService.deleteNavigation(id);
            if (success) {
                response.put("success", true);
                response.put("message", "导航链接删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "导航链接删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除导航链接时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 根据ID获取导航链接
     * @param id 导航链接ID
     * @return 导航链接对象
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getNavigationById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            Navigation navigation = navigationService.getNavigationById(id);
            if (navigation != null) {
                response.put("success", true);
                response.put("data", navigation);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "导航链接不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取导航链接时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 分页查询导航链接
     * @param page 页码（默认1）
     * @param size 每页大小（默认10）
     * @param category 分类（可选）
     * @param keyword 搜索关键词（可选）
     * @return 分页结果
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getNavigations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String keyword) {
        Map<String, Object> response = new HashMap<>();
        try {
            Page<Navigation> pageResult = navigationService.getNavigationPage(page, size, category, keyword);
            response.put("success", true);
            response.put("data", pageResult.getRecords());
            response.put("total", pageResult.getTotal());
            response.put("page", pageResult.getCurrent());
            response.put("size", pageResult.getSize());
            response.put("pages", pageResult.getPages());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取导航链接列表时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取所有导航链接（不分页）
     * @return 导航链接列表
     */
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllNavigations() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Navigation> navigations = navigationService.getAllNavigations();
            response.put("success", true);
            response.put("data", navigations);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取所有导航链接时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取所有分类
     * @return 分类列表
     */
    @GetMapping("/categories")
    public ResponseEntity<Map<String, Object>> getAllCategories() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<String> categories = navigationService.getAllCategories();
            response.put("success", true);
            response.put("data", categories);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取分类列表时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量删除导航链接
     * @param ids 导航链接ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteNavigations(@RequestBody List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = navigationService.batchDeleteNavigations(ids);
            if (success) {
                response.put("success", true);
                response.put("message", "批量删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "批量删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量删除时发生错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}