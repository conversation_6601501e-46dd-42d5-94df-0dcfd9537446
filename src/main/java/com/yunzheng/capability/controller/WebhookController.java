package com.yunzheng.capability.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunzheng.capability.entity.WebhookConfig;
import com.yunzheng.capability.entity.WebhookLog;
import com.yunzheng.capability.service.WebhookService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Webhook控制器
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/api/webhook")
public class WebhookController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebhookController.class);
    
    @Autowired
    private WebhookService webhookService;
    
    /**
     * 接收GitLab Webhook数据
     */
    @PostMapping("/gitlab")
    public ResponseEntity<Map<String, Object>> receiveGitLabWebhook(@RequestBody Map<String, Object> payload) {
        logger.info("收到GitLab Webhook数据: {}", payload.keySet());
        
        try {
            WebhookLog log = webhookService.processWebhook("gitlab", payload);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Webhook数据已接收并处理");
            response.put("logId", log.getId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("处理GitLab Webhook失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "处理Webhook数据失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 接收GitHub Webhook数据
     */
    @PostMapping("/github")
    public ResponseEntity<Map<String, Object>> receiveGitHubWebhook(@RequestBody Map<String, Object> payload) {
        logger.info("收到GitHub Webhook数据: {}", payload.keySet());
        
        try {
            WebhookLog log = webhookService.processWebhook("github", payload);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Webhook数据已接收并处理");
            response.put("logId", log.getId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("处理GitHub Webhook失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "处理Webhook数据失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 通用Webhook接收接口 - 可以接收任何JSON数据
     */
    @PostMapping("/generic")
    public ResponseEntity<Map<String, Object>> receiveGenericWebhook(
            @RequestBody Map<String, Object> payload,
            @RequestParam(defaultValue = "generic") String source) {
        
        logger.info("收到来自 {} 的Webhook数据: {}", source, payload.keySet());
        
        try {
            WebhookLog log = webhookService.processWebhook(source, payload);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Webhook数据已接收并处理");
            response.put("logId", log.getId());
            response.put("source", source);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("处理通用Webhook失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "处理Webhook数据失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 查询Webhook日志列表
     */
    @GetMapping("/logs")
    public ResponseEntity<Page<WebhookLog>> getWebhookLogs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        try {
            Page<WebhookLog> logs = webhookService.getWebhookLogs(page, size);
            return ResponseEntity.ok(logs);
        } catch (Exception e) {
            logger.error("查询Webhook日志失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据来源查询Webhook日志
     */
    @GetMapping("/logs/source/{source}")
    public ResponseEntity<List<WebhookLog>> getWebhookLogsBySource(@PathVariable String source) {
        try {
            List<WebhookLog> logs = webhookService.getWebhookLogsBySource(source);
            return ResponseEntity.ok(logs);
        } catch (Exception e) {
            logger.error("根据来源查询Webhook日志失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 重新推送失败的消息
     */
    @PostMapping("/retry/{logId}")
    public ResponseEntity<Map<String, Object>> retryFailedPush(@PathVariable Long logId) {
        try {
            webhookService.retryFailedPush(logId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "重新推送已触发");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("重新推送失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "重新推送失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取所有Webhook配置
     */
    @GetMapping("/configs")
    public ResponseEntity<List<WebhookConfig>> getAllWebhookConfigs() {
        try {
            List<WebhookConfig> configs = webhookService.getAllWebhookConfigs();
            return ResponseEntity.ok(configs);
        } catch (Exception e) {
            logger.error("获取Webhook配置失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 保存Webhook配置
     */
    @PostMapping("/configs")
    public ResponseEntity<WebhookConfig> saveWebhookConfig(@RequestBody WebhookConfig config) {
        try {
            WebhookConfig savedConfig = webhookService.saveWebhookConfig(config);
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            logger.error("保存Webhook配置失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 删除Webhook配置
     */
    @DeleteMapping("/configs/{id}")
    public ResponseEntity<Map<String, Object>> deleteWebhookConfig(@PathVariable Long id) {
        try {
            webhookService.deleteWebhookConfig(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "配置已删除");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除Webhook配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除配置失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
