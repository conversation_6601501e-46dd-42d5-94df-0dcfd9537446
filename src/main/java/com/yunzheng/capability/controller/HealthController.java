package com.yunzheng.capability.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 用于Docker容器的健康检查
 */
@RestController
public class HealthController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 健康检查端点
     * 检查应用和数据库连接状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("timestamp", System.currentTimeMillis());
        
        // 检查数据库连接
        try {
            Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            Map<String, Object> dbStatus = new HashMap<>();
            dbStatus.put("status", "UP");
            dbStatus.put("message", "Database connection is healthy");
            status.put("database", dbStatus);
        } catch (Exception e) {
            Map<String, Object> dbStatus = new HashMap<>();
            dbStatus.put("status", "DOWN");
            dbStatus.put("message", "Database connection failed: " + e.getMessage());
            status.put("database", dbStatus);
            // 即使数据库连接失败，应用仍然可以启动，所以不改变整体状态
        }
        
        return ResponseEntity.ok(status);
    }
}