package com.yunzheng.capability.controller;

import com.yunzheng.capability.entity.ScriptTemplate;
import com.yunzheng.capability.service.ScriptTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/templates")
public class ScriptTemplateController {
    
    @Autowired
    private ScriptTemplateService scriptTemplateService;
    
    /**
     * 获取所有脚本模板
     */
    @GetMapping
    public ResponseEntity<List<ScriptTemplate>> getAllTemplates() {
        try {
            List<ScriptTemplate> templates = scriptTemplateService.getAllTemplates();
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据ID获取脚本模板
     */
    @GetMapping("/{id}")
    public ResponseEntity<ScriptTemplate> getTemplateById(@PathVariable Long id) {
        try {
            Optional<ScriptTemplate> template = scriptTemplateService.getTemplateById(id);
            return template.map(ResponseEntity::ok)
                          .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 创建新的脚本模板
     */
    @PostMapping
    public ResponseEntity<?> createTemplate(@RequestBody ScriptTemplate template) {
        try {
            ScriptTemplate created = scriptTemplateService.createTemplate(template);
            return ResponseEntity.ok(created);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("创建脚本模板失败");
        }
    }
    
    /**
     * 更新脚本模板
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTemplate(@PathVariable Long id, @RequestBody ScriptTemplate template) {
        try {
            template.setId(id);
            ScriptTemplate updated = scriptTemplateService.updateTemplate(template);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("更新脚本模板失败");
        }
    }
    
    /**
     * 删除脚本模板
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTemplate(@PathVariable Long id) {
        try {
            scriptTemplateService.deleteTemplate(id);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("删除脚本模板失败");
        }
    }
    
    /**
     * 根据分类获取模板
     */
    @GetMapping("/category/{category}")
    public ResponseEntity<List<ScriptTemplate>> getTemplatesByCategory(@PathVariable String category) {
        try {
            List<ScriptTemplate> templates = scriptTemplateService.getTemplatesByCategory(category);
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 搜索模板
     */
    @GetMapping("/search")
    public ResponseEntity<List<ScriptTemplate>> searchTemplates(@RequestParam String keyword) {
        try {
            List<ScriptTemplate> templates = scriptTemplateService.searchTemplates(keyword);
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取公开模板
     */
    @GetMapping("/public")
    public ResponseEntity<List<ScriptTemplate>> getPublicTemplates() {
        try {
            List<ScriptTemplate> templates = scriptTemplateService.getPublicTemplates();
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据创建者获取模板
     */
    @GetMapping("/user/{createdBy}")
    public ResponseEntity<List<ScriptTemplate>> getTemplatesByCreatedBy(@PathVariable String createdBy) {
        try {
            List<ScriptTemplate> templates = scriptTemplateService.getTemplatesByCreatedBy(createdBy);
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    public ResponseEntity<List<String>> getAllCategories() {
        try {
            List<String> categories = scriptTemplateService.getAllCategories();
            return ResponseEntity.ok(categories);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
}
