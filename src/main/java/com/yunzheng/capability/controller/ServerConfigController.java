package com.yunzheng.capability.controller;

import com.yunzheng.capability.entity.ServerConfig;
import com.yunzheng.capability.service.ServerConfigService;
import com.yunzheng.capability.service.SSHService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/servers")
public class ServerConfigController {
    
    @Autowired
    private ServerConfigService serverConfigService;
    
    @Autowired
    private SSHService sshService;
    
    /**
     * 服务器健康检查端点
     */
    @GetMapping("/health/status")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("OK");
    }
    
    /**
     * 获取所有服务器配置
     */
    @GetMapping
    public ResponseEntity<List<ServerConfig>> getAllServers() {
        try {
            List<ServerConfig> servers = serverConfigService.getAllServers();
            return ResponseEntity.ok(servers);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 根据ID获取服务器配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<ServerConfig> getServerById(@PathVariable Long id) {
        try {
            Optional<ServerConfig> server = serverConfigService.getServerById(id);
            return server.map(ResponseEntity::ok)
                        .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 创建新的服务器配置
     */
    @PostMapping
    public ResponseEntity<?> createServer(@RequestBody ServerConfig serverConfig) {
        try {
            ServerConfig created = serverConfigService.createServer(serverConfig);
            return ResponseEntity.ok(created);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("创建服务器配置失败");
        }
    }
    
    /**
     * 更新服务器配置
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateServer(@PathVariable Long id, @RequestBody ServerConfig serverConfig) {
        try {
            ServerConfig updated = serverConfigService.updateServer(id, serverConfig);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("更新服务器配置失败");
        }
    }
    
    /**
     * 删除服务器配置
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteServer(@PathVariable Long id) {
        try {
            serverConfigService.deleteServer(id);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("删除服务器配置失败");
        }
    }
    
    /**
     * 搜索服务器
     */
    @GetMapping("/search")
    public ResponseEntity<List<ServerConfig>> searchServers(@RequestParam String name) {
        try {
            List<ServerConfig> servers = serverConfigService.searchServersByName(name);
            return ResponseEntity.ok(servers);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 测试服务器连接
     */
    @PostMapping("/{id}/test")
    public ResponseEntity<?> testConnection(@PathVariable Long id, @RequestBody java.util.Map<String, String> request) {
        try {
            Optional<ServerConfig> serverConfigOpt = serverConfigService.getServerById(id);
            if (serverConfigOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            String username = request.get("username");
            String password = request.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("密码不能为空");
            }
            
            boolean connected = sshService.testConnection(serverConfigOpt.get(), username, password);
            if (connected) {
                return ResponseEntity.ok().body("连接成功");
            } else {
                return ResponseEntity.badRequest().body("连接失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("测试连接失败: " + e.getMessage());
        }
    }
}
