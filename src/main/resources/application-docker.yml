server:
  port: 7799

spring:
  datasource:
    url: ******************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
  sql:
    init:
      mode: never
      schema-locations: classpath:schema-mysql.sql
      continue-on-error: true
      encoding: UTF-8

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    use-generated-keys: false
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.yunzheng.capability: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc.datasource.init: DEBUG
    org.springframework.web: DEBUG
