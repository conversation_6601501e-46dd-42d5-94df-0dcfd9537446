-- 创建服务器配置表
CREATE TABLE IF NOT EXISTS server_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL DEFAULT 22,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建脚本执行记录表
CREATE TABLE IF NOT EXISTS script_execution (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INTEGER NOT NULL,
    script_content TEXT NOT NULL,
    execution_result TEXT,
    error_message TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'RUNNING',
    execution_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES server_config(id)
);

-- 创建脚本模板表
CREATE TABLE IF NOT EXISTS script_template (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    script_content TEXT NOT NULL,
    description TEXT,
    category VARCHAR(100),
    is_public BOOLEAN DEFAULT FALSE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建导航链接表
CREATE TABLE IF NOT EXISTS navigation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    icon VARCHAR(500),
    sort INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_server_config_name ON server_config(name);
CREATE INDEX idx_server_config_host ON server_config(host);
CREATE INDEX idx_script_execution_server_id ON script_execution(server_id);
CREATE INDEX idx_script_execution_status ON script_execution(status);
CREATE INDEX idx_script_execution_created_at ON script_execution(created_at);
CREATE INDEX idx_script_template_category ON script_template(category);
CREATE INDEX idx_script_template_created_by ON script_template(created_by);
CREATE INDEX idx_script_template_is_public ON script_template(is_public);
CREATE INDEX idx_navigation_category ON navigation(category);
CREATE INDEX idx_navigation_sort ON navigation(sort);
CREATE INDEX idx_navigation_name ON navigation(name);

-- 插入测试数据（使用INSERT IGNORE替代PostgreSQL的ON CONFLICT DO NOTHING）
INSERT IGNORE INTO server_config (id, name, host, port, description) VALUES 
(1, '测试服务器1', '*************', 22, '这是一个测试服务器'),
(2, '测试服务器2', '*************', 22, '这是另一个测试服务器');

INSERT IGNORE INTO script_template (id, name, script_content, description, category, is_public, created_by) VALUES 
(1, '系统信息检查', 'uname -a && cat /etc/os-release', '检查系统基本信息', '系统管理', TRUE, 'admin'),
(2, '磁盘空间检查', 'df -h', '检查磁盘使用情况', '系统管理', TRUE, 'admin'),
(3, '内存使用检查', 'free -h', '检查内存使用情况', '系统管理', TRUE, 'admin');

-- 创建Webhook配置表
CREATE TABLE IF NOT EXISTS webhook_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    feishu_webhook_url VARCHAR(1000) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建Webhook日志表
CREATE TABLE IF NOT EXISTS webhook_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    source VARCHAR(100) NOT NULL,
    json_data LONGTEXT NOT NULL,
    push_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    push_result TEXT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建Webhook相关索引
CREATE INDEX idx_webhook_config_name ON webhook_config(name);
CREATE INDEX idx_webhook_config_enabled ON webhook_config(enabled);
CREATE INDEX idx_webhook_log_source ON webhook_log(source);
CREATE INDEX idx_webhook_log_status ON webhook_log(push_status);
CREATE INDEX idx_webhook_log_created_at ON webhook_log(created_at);

INSERT IGNORE INTO navigation (id, name, url, category, description, icon, sort) VALUES 
(1, 'Google', 'https://www.google.com', '搜索引擎', '全球最大的搜索引擎', 'https://www.google.com/favicon.ico', 1),
(2, 'GitHub', 'https://github.com', '开发工具', '全球最大的代码托管平台', 'https://github.com/favicon.ico', 2),
(3, 'Stack Overflow', 'https://stackoverflow.com', '开发工具', '程序员问答社区', 'https://stackoverflow.com/favicon.ico', 3),
(4, '百度', 'https://www.baidu.com', '搜索引擎', '中国最大的搜索引擎', 'https://www.baidu.com/favicon.ico', 4),
(5, 'Vue.js', 'https://vuejs.org', '前端框架', 'Vue.js官方文档', 'https://vuejs.org/logo.svg', 5);

-- 插入默认的Webhook配置示例（请根据实际情况修改飞书webhook地址）
INSERT IGNORE INTO webhook_config (id, name, feishu_webhook_url, enabled, description) VALUES 
(1, '默认飞书群', 'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-key', FALSE, '默认的飞书群通知配置，请修改为实际的webhook地址');

-- MySQL不需要手动设置自增序列值，自增列会自动从最大值+1开始