package com.yunzheng.capability;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
public class PostgresqlConnectionTest {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    public void testDatabaseConnection() throws SQLException {
        // 测试数据源是否注入成功
        assertNotNull(dataSource, "DataSource should not be null");
        
        // 测试是否能获取连接
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertTrue(connection.isValid(1), "Connection should be valid");
            
            // 输出连接信息
            System.out.println("Successfully connected to: " + connection.getMetaData().getURL());
            System.out.println("Database product name: " + connection.getMetaData().getDatabaseProductName());
            System.out.println("Database product version: " + connection.getMetaData().getDatabaseProductVersion());
        }
    }

    @Test
    public void testQueryExecution() {
        // 测试简单查询
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM server_config", Integer.class);
        assertNotNull(count, "Query result should not be null");
        System.out.println("Number of records in server_config table: " + count);
    }
}