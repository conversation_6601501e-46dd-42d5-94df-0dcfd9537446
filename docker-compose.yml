version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: yz-ci-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=yz_ci
    volumes:
      - type: bind
        source: ./mysql-data
        target: /var/lib/mysql
        bind:
          create_host_path: true
    networks:
      - yz-ci-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端应用 (yz-ci-a)
  frontend:
    image: harbor.yunzheng.work/yunzheng/yz-ci-a
    container_name: yz-ci-frontend
    ports:
      - "8088:80"  # 避免常用端口8080
    environment:
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - yz-ci-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端服务 (yz-ci-b)
  backend:
    image: harbor.yunzheng.work/yunzheng/yz-ci-b
    container_name: yz-ci-backend
    ports:
      - "7799:7799"  # 保持原有端口
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=7799
    depends_on:
      - mysql
    volumes:
      - type: bind
        source: ./data
        target: /app/data
        bind:
          create_host_path: true
      - type: bind
        source: ./logs
        target: /app/logs
        bind:
          create_host_path: true
    networks:
      - yz-ci-network
    restart: unless-stopped

networks:
  yz-ci-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16