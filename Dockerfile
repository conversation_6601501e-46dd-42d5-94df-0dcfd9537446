# =========
# Build stage - Backend
# =========
FROM maven:3.9-eclipse-temurin-21 AS backend-build

WORKDIR /workspace

# 复制Maven配置文件用于私有仓库
RUN mkdir -p /root/.m2
COPY build/settings.xml /root/.m2/settings.xml

# 仅拷贝依赖相关文件以利用缓存
COPY pom.xml ./
# 下载依赖（如果POM文件未更改，此层将被缓存）
RUN --mount=type=cache,target=/root/.m2/repository mvn -B -q -DskipTests dependency:go-offline

# 拷贝完整源码并构建
COPY . ./
RUN --mount=type=cache,target=/root/.m2/repository mvn -B -DskipTests package

# ============
# Runtime stage
# ============
FROM eclipse-temurin:21-jre-alpine

ENV TZ=Asia/Shanghai \
    JAVA_OPTS=""

WORKDIR /app

# 复制后端JAR
COPY --from=backend-build /workspace/target/*-SNAPSHOT.jar /app/app.jar

# 创建必要的目录
RUN mkdir -p /app/data /app/logs

# 暴露端口
EXPOSE 7799

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:7799/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar --spring.profiles.active=docker"]


