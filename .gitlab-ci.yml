stages:
  - build

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1
  HARBOR_REGISTRY: harbor.yunzheng.work
  HARBOR_PROJECT: yunzheng
  HARBOR_IMAGE_NAME: yz-ci-b
  IMAGE_TAG: 1.0.1

docker-build-push:
  stage: build
  image: docker:latest
  tags:
    - cloud-dev
  services:
    - name: docker:dind
      alias: docker
      command: ["--insecure-registry=$HARBOR_REGISTRY"]
  before_script:
    - mkdir -p ~/.docker
    - echo '{"experimental":"enabled","insecure-registries":["$HARBOR_REGISTRY"]}' > ~/.docker/config.json
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin
  script:
    - docker build --add-host maven.yunzheng.work:************ -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$IMAGE_TAG .
    - docker tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$IMAGE_TAG $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$IMAGE_TAG
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$IMAGE_TAG"
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest"
  only:
    - dev
    - main