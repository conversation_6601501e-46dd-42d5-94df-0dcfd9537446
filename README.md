# YZ CI - 服务器脚本执行系统

一个基于Spring Boot + Vue2 + Element UI的服务器管理和脚本执行系统，支持SSH连接到远程服务器执行各种脚本命令。

## 功能特性

- 🖥️ **服务器管理**: 添加、编辑、删除服务器配置
- 🔐 **SSH连接**: 支持SSH密码认证连接远程服务器
- 📝 **脚本执行**: 在线编辑和执行各种脚本命令
- 📊 **执行历史**: 查看脚本执行历史记录和结果
- 🎯 **脚本模板**: 提供常用脚本模板快速使用
- 🔍 **结果展示**: 实时显示脚本执行结果和错误信息
- 💾 **SQLite数据库**: 轻量级数据存储，无需额外配置

## 技术栈

### 后端
- Java 21
- Spring Boot 3.2.0
- Spring Data JPA
- SQLite数据库
- JSch (SSH客户端)

### 前端
- Vue 2.6.14
- Element UI 2.15.13
- Vue Router
- Vuex
- Axios

## 快速开始

### 环境要求
- Java 21
- Node.js 16+
- Maven 3.6+

### 1. 克隆项目
```bash
git clone <repository-url>
cd yz-ci
```

### 2. 启动后端

```bash
# 编译项目
mvn clean compile

# 启动Spring Boot应用
mvn spring-boot:run
```

后端服务将在 http://localhost:8091 启动

### 3. 启动前端

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

前端服务将在 http://localhost:3000 启动

### 4. 访问应用

打开浏览器访问 http://localhost:3000

## 使用说明

### 服务器管理
1. 点击"服务器管理"菜单
2. 点击"添加服务器"按钮
3. 填写服务器信息：
   - 服务器名称
   - 主机地址（IP或域名）
   - SSH端口（默认22）
   - 用户名
   - 密码
   - 描述（可选）
4. 点击"测试连接"验证配置
5. 保存服务器配置

### 脚本执行
1. 点击"脚本执行"菜单
2. 选择目标服务器
3. 在脚本内容区域输入要执行的命令
4. 可以使用提供的脚本模板
5. 点击"执行脚本"
6. 查看执行结果

### 执行历史
1. 点击"执行历史"菜单
2. 查看所有脚本执行记录
3. 可以按服务器、状态、日期筛选
4. 点击记录查看详细信息
5. 支持重新执行历史脚本

## 脚本模板

系统提供以下常用脚本模板：

- **系统信息**: 查看系统基本信息
- **进程监控**: 查看系统进程状态
- **日志查看**: 查看系统日志
- **磁盘清理**: 清理临时文件
- **网络诊断**: 网络连接测试
- **服务状态**: 查看服务运行状态

## API接口

### 服务器管理
- `GET /api/servers` - 获取所有服务器
- `POST /api/servers` - 创建服务器
- `PUT /api/servers/{id}` - 更新服务器
- `DELETE /api/servers/{id}` - 删除服务器
- `POST /api/servers/{id}/test` - 测试连接

### 脚本执行
- `POST /api/executions/execute` - 执行脚本
- `GET /api/executions` - 获取执行记录
- `GET /api/executions/{id}` - 获取单个执行记录
- `GET /api/executions/recent` - 获取最近执行记录

## 数据库结构

### server_config 表
- id: 主键
- name: 服务器名称
- host: 主机地址
- port: SSH端口
- username: 用户名
- password: 密码
- description: 描述
- created_at: 创建时间
- updated_at: 更新时间

### script_execution 表
- id: 主键
- server_id: 服务器ID
- script_content: 脚本内容
- execution_result: 执行结果
- error_message: 错误信息
- status: 执行状态 (RUNNING/SUCCESS/FAILED)
- execution_time: 执行耗时(毫秒)
- created_at: 创建时间

## 注意事项

1. **安全性**: 服务器密码以明文存储，生产环境建议使用密钥认证
2. **权限**: 确保SSH用户有执行相应命令的权限
3. **网络**: 确保应用服务器能够访问目标服务器的SSH端口
4. **防火墙**: 检查防火墙设置，确保SSH连接正常

## 开发

### 添加新功能
1. 后端：在相应的Controller、Service、Repository中添加代码
2. 前端：在views目录下添加新页面，在router中配置路由
3. 更新API文档

### 构建生产版本
```bash
# 构建前端
cd frontend
npm run build

# 构建后端
mvn clean package

# 运行JAR文件
java -jar target/yz-ci-1.0-SNAPSHOT.jar
```

## 许可证

MIT License
